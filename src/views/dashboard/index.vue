<template>
  <div class="dashboard-container">
    <!-- 欢迎信息 -->
    <div class="welcome-section">
      <h2>欢迎回来, {{ name }}!</h2>
      <p>这里是您的数据统计概览</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <i class="el-icon-loading" style="font-size: 24px;" />
      <p>正在加载统计数据...</p>
    </div>

    <!-- 统计数据展示 -->
    <div v-else-if="statsData" class="stats-content">
      <!-- 关键指标卡片 -->
      <div class="metrics-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-icon users">
                <i class="el-icon-user" />
              </div>
              <div class="metric-info">
                <h3>{{ statsData.totalUsers }}</h3>
                <p>总用户数</p>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-icon views">
                <i class="el-icon-view" />
              </div>
              <div class="metric-info">
                <h3>{{ formatNumber(statsData.totalViews) }}</h3>
                <p>总观看数</p>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-icon content">
                <i class="el-icon-plus" />
              </div>
              <div class="metric-info">
                <h3>{{ statsData.monthlyNewContent }}</h3>
                <p>本月新增内容</p>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-icon rating">
                <i class="el-icon-star-on" />
              </div>
              <div class="metric-info">
                <h3>{{ statsData.averageRating }}</h3>
                <p>平均评分</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <el-row :gutter="20">
          <!-- 内容类型分布饼图 -->
          <el-col :span="12">
            <div class="chart-card">
              <h3>内容类型分布</h3>
              <div ref="pieChart" class="chart" style="width: 100%; height: 350px; background: #f9f9f9;" />
            </div>
          </el-col>

          <!-- 热门类型排行柱状图 -->
          <el-col :span="12">
            <div class="chart-card">
              <h3>热门类型排行</h3>
              <div ref="barChart" class="chart" style="width: 100%; height: 350px; background: #f9f9f9;" />
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-alert
        title="数据加载失败"
        type="error"
        description="无法获取统计数据，请稍后重试"
        show-icon
        :closable="false"
      />
      <el-button type="primary" style="margin-top: 20px" @click="fetchStats">
        重新加载
      </el-button>
    </div>

    <!-- 调试信息 -->
    <!-- <div style="margin-bottom: 20px; padding: 15px; background: #f0f0f0; border-radius: 4px;">
      <h4>调试信息:</h4>
      <p><strong>数据状态:</strong> {{ statsData ? '已加载' : '未加载' }}</p>
      <p v-if="statsData"><strong>原始数据:</strong></p>
      <pre v-if="statsData" style="font-size: 12px; max-height: 200px; overflow-y: auto;">{{ JSON.stringify(statsData, null, 2) }}</pre>
      <el-button type="primary" @click="initCharts">重新初始化图表</el-button>
    </div> -->
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import * as echarts from 'echarts'
import { getStats } from '@/api/stats'

export default {
  name: 'Dashboard',
  data() {
    return {
      loading: false,
      statsData: null,
      pieChart: null,
      barChart: null
    }
  },
  computed: {
    ...mapGetters(['name'])
  },
  mounted() {
    this.fetchStats()
    // 添加窗口 resize 事件监听器
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize)
    // 销毁图表实例
    if (this.pieChart) {
      this.pieChart.dispose()
    }
    if (this.barChart) {
      this.barChart.dispose()
    }
  },
  methods: {
    // 获取统计数据
    async fetchStats() {
      this.loading = true
      try {
        const response = await getStats()
        console.log('Stats API Response:', response)
        this.statsData = response.data
        // 等待数据更新和DOM重新渲染后初始化图表
        this.$nextTick(() => {
          console.log('Initializing charts with data:', this.statsData)
          // 使用更长的延迟确保 DOM 完全渲染
          setTimeout(() => {
            this.initCharts()
          }, 300)
        })
      } catch (error) {
        console.error('获取统计数据失败:', error)
        this.$message.error('获取统计数据失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    // 初始化图表
    initCharts() {
      if (!this.statsData) {
        console.warn('No stats data available for charts')
        return
      }
      console.log('Initializing charts...')
      this.initPieChart()
      this.initBarChart()
    },
    // 初始化饼图
    initPieChart() {
      console.log('Attempting to initialize pie chart...')
      const element = this.$refs.pieChart
      if (!element) {
        console.error('pieChart ref not found')
        return
      }

      console.log('Pie chart element found:', element)
      console.log('Element dimensions:', element.offsetWidth, 'x', element.offsetHeight)

      if (this.pieChart) {
        this.pieChart.dispose()
      }

      try {
        this.pieChart = echarts.init(element)
        console.log('Pie chart instance created:', this.pieChart)

        const option = {
          title: {
            text: '内容类型分布',
            left: 'center'
          },
          tooltip: {
            trigger: 'item'
          },
          legend: {
            orient: 'vertical',
            left: 'left'
          },
          series: [{
            name: '内容类型',
            type: 'pie',
            radius: '50%',
            data: [
              { value: this.statsData.totalMovies, name: '电影' },
              { value: this.statsData.totalTVShows, name: '电视剧' },
              { value: this.statsData.totalVarietyShows, name: '综艺节目' }
            ]
          }]
        }

        this.pieChart.setOption(option)
        console.log('Pie chart option set successfully')
      } catch (error) {
        console.error('Error initializing pie chart:', error)
      }
    },
    // 初始化柱状图
    initBarChart() {
      console.log('Attempting to initialize bar chart...')
      const element = this.$refs.barChart
      if (!element) {
        console.error('barChart ref not found')
        return
      }

      console.log('Bar chart element found:', element)
      console.log('Element dimensions:', element.offsetWidth, 'x', element.offsetHeight)

      if (this.barChart) {
        this.barChart.dispose()
      }

      try {
        this.barChart = echarts.init(element)
        console.log('Bar chart instance created:', this.barChart)

        const genres = this.statsData.topGenres || []
        const names = genres.map(item => item.name)
        const counts = genres.map(item => item.count)

        console.log('Bar chart data - genres:', genres)
        console.log('Bar chart data - names:', names)
        console.log('Bar chart data - counts:', counts)

        const option = {
          title: {
            text: '热门类型排行',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: names
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            name: '内容数量',
            type: 'bar',
            data: counts,
            itemStyle: {
              color: '#409EFF'
            }
          }]
        }

        this.barChart.setOption(option)
        console.log('Bar chart option set successfully')
      } catch (error) {
        console.error('Error initializing bar chart:', error)
      }
    },
    // 处理窗口大小变化
    handleResize() {
      if (this.pieChart) {
        this.pieChart.resize()
      }
      if (this.barChart) {
        this.barChart.resize()
      }
    },
    // 格式化数字显示
    formatNumber(num) {
      if (num >= 100000000) {
        return (num / 100000000).toFixed(1) + '亿'
      } else if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toString()
    }
  }
}
</script>
<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 8px;
  margin-bottom: 20px;

  h2 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
  }

  p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;

  p {
    margin-top: 20px;
    color: #666;
    font-size: 16px;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px;
}

.stats-content {
  .metrics-cards {
    margin-bottom: 30px;

    .metric-card {
      background: white;
      border-radius: 8px;
      padding: 25px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
      }

      .metric-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;

        i {
          font-size: 24px;
          color: white;
        }

        &.users {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.views {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.content {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.rating {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }

      .metric-info {
        flex: 1;

        h3 {
          margin: 0 0 5px 0;
          font-size: 32px;
          font-weight: 700;
          color: #333;
        }

        p {
          margin: 0;
          font-size: 14px;
          color: #666;
        }
      }
    }
  }

  .charts-section {
    .chart-card {
      background: white;
      border-radius: 8px;
      padding: 25px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      h3 {
        margin: 0 0 20px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
        text-align: center;
      }

      .chart {
        width: 100%;
        height: 350px;
        min-height: 350px;
        background-color: #fafafa;
        border: 1px solid #eee;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .dashboard-container {
    .metrics-cards {
      .el-col {
        margin-bottom: 20px;
      }
    }

    .charts-section {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;

    .welcome-section {
      padding: 20px;

      h2 {
        font-size: 24px;
      }
    }

    .stats-content {
      .metrics-cards {
        .metric-card {
          padding: 20px;

          .metric-icon {
            width: 50px;
            height: 50px;
            margin-right: 15px;

            i {
              font-size: 20px;
            }
          }

          .metric-info {
            h3 {
              font-size: 24px;
            }
          }
        }
      }

      .charts-section {
        .chart-card {
          padding: 20px;

          .chart {
            height: 300px;
          }
        }
      }
    }
  }
}
</style>
