<template>
  <div class="echarts-test">
    <h2>ECharts 测试页面</h2>
    
    <div class="test-section">
      <h3>测试 1: 基础饼图</h3>
      <div ref="testPieChart" class="test-chart"></div>
      <button @click="initTestPieChart">初始化饼图</button>
    </div>
    
    <div class="test-section">
      <h3>测试 2: 基础柱状图</h3>
      <div ref="testBarChart" class="test-chart"></div>
      <button @click="initTestBarChart">初始化柱状图</button>
    </div>
    
    <div class="test-section">
      <h3>测试 3: 统计数据图表</h3>
      <button @click="fetchAndRenderCharts">获取数据并渲染图表</button>
      <div v-if="statsData">
        <p>数据已加载: {{ Object.keys(statsData).length }} 个字段</p>
        <div class="charts-row">
          <div class="chart-container">
            <h4>内容类型分布</h4>
            <div ref="statsPieChart" class="test-chart"></div>
          </div>
          <div class="chart-container">
            <h4>热门类型排行</h4>
            <div ref="statsBarChart" class="test-chart"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getStats } from '@/api/stats'

export default {
  name: 'EChartsTest',
  data() {
    return {
      statsData: null,
      testPieChart: null,
      testBarChart: null,
      statsPieChart: null,
      statsBarChart: null
    }
  },
  beforeDestroy() {
    if (this.testPieChart) this.testPieChart.dispose()
    if (this.testBarChart) this.testBarChart.dispose()
    if (this.statsPieChart) this.statsPieChart.dispose()
    if (this.statsBarChart) this.statsBarChart.dispose()
  },
  methods: {
    initTestPieChart() {
      console.log('Initializing test pie chart...')
      const element = this.$refs.testPieChart
      if (!element) {
        console.error('Test pie chart element not found')
        return
      }
      
      if (this.testPieChart) {
        this.testPieChart.dispose()
      }
      
      this.testPieChart = echarts.init(element)
      
      const option = {
        title: {
          text: '测试饼图',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [{
          name: '测试数据',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 30, name: '项目A' },
            { value: 20, name: '项目B' },
            { value: 15, name: '项目C' }
          ]
        }]
      }
      
      this.testPieChart.setOption(option)
      console.log('Test pie chart initialized successfully')
    },
    
    initTestBarChart() {
      console.log('Initializing test bar chart...')
      const element = this.$refs.testBarChart
      if (!element) {
        console.error('Test bar chart element not found')
        return
      }
      
      if (this.testBarChart) {
        this.testBarChart.dispose()
      }
      
      this.testBarChart = echarts.init(element)
      
      const option = {
        title: {
          text: '测试柱状图',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['类型A', '类型B', '类型C']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '数量',
          type: 'bar',
          data: [16, 15, 13]
        }]
      }
      
      this.testBarChart.setOption(option)
      console.log('Test bar chart initialized successfully')
    },
    
    async fetchAndRenderCharts() {
      console.log('Fetching stats data...')
      try {
        const response = await getStats()
        console.log('Stats response:', response)
        this.statsData = response.data
        
        this.$nextTick(() => {
          this.initStatsPieChart()
          this.initStatsBarChart()
        })
      } catch (error) {
        console.error('Error fetching stats:', error)
      }
    },
    
    initStatsPieChart() {
      console.log('Initializing stats pie chart...')
      const element = this.$refs.statsPieChart
      if (!element) {
        console.error('Stats pie chart element not found')
        return
      }
      
      if (this.statsPieChart) {
        this.statsPieChart.dispose()
      }
      
      this.statsPieChart = echarts.init(element)
      
      const option = {
        title: {
          text: '内容类型分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [{
          name: '内容类型',
          type: 'pie',
          radius: '50%',
          data: [
            { value: this.statsData.totalMovies, name: '电影' },
            { value: this.statsData.totalTVShows, name: '电视剧' },
            { value: this.statsData.totalVarietyShows, name: '综艺节目' }
          ]
        }]
      }
      
      this.statsPieChart.setOption(option)
      console.log('Stats pie chart initialized successfully')
    },
    
    initStatsBarChart() {
      console.log('Initializing stats bar chart...')
      const element = this.$refs.statsBarChart
      if (!element) {
        console.error('Stats bar chart element not found')
        return
      }
      
      if (this.statsBarChart) {
        this.statsBarChart.dispose()
      }
      
      this.statsBarChart = echarts.init(element)
      
      const genres = this.statsData.topGenres || []
      const names = genres.map(item => item.name)
      const counts = genres.map(item => item.count)
      
      const option = {
        title: {
          text: '热门类型排行',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: names
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '内容数量',
          type: 'bar',
          data: counts
        }]
      }
      
      this.statsBarChart.setOption(option)
      console.log('Stats bar chart initialized successfully')
    }
  }
}
</script>

<style scoped>
.echarts-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-chart {
  width: 100%;
  height: 300px;
  border: 1px solid #ccc;
  margin: 10px 0;
}

.charts-row {
  display: flex;
  gap: 20px;
}

.chart-container {
  flex: 1;
}

button {
  padding: 8px 16px;
  background: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin: 10px 5px;
}

button:hover {
  background: #66b1ff;
}

h2, h3, h4 {
  color: #333;
}
</style>
