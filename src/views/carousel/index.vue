<template>
  <div class="carousel-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-content">
        <div>
          <h2>轮播图管理</h2>
          <p>管理网站首页轮播图的增删改查</p>
        </div>
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
          新增轮播图
        </el-button>
      </div>
    </div>

    <!-- 轮播图列表 -->
    <div class="carousel-list">
      <el-table
        v-loading="loading"
        :data="carouselList"
        style="width: 100%"
        row-key="id"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="图片" width="120">
          <template slot-scope="scope">
            <img
              :src="scope.row.image"
              :alt="scope.row.title"
              class="carousel-thumb"
            />
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="url" label="跳转链接" min-width="150" show-overflow-tooltip />
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template slot-scope="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleView(scope.row)">查看</el-button>
            <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="carouselForm"
        :model="carouselForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="carouselForm.title" placeholder="请输入轮播图标题" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="carouselForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入轮播图描述"
          />
        </el-form-item>
        <el-form-item label="图片URL" prop="image">
          <el-input v-model="carouselForm.image" placeholder="请输入图片URL" />
        </el-form-item>
        <el-form-item label="跳转链接" prop="url">
          <el-input v-model="carouselForm.url" placeholder="请输入跳转链接" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number
            v-model="carouselForm.sortOrder"
            :min="0"
            :max="999"
            placeholder="排序数字"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="carouselForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      title="轮播图详情"
      :visible.sync="viewDialogVisible"
      width="500px"
    >
      <div v-if="currentCarousel" class="carousel-detail">
        <div class="detail-image">
          <img :src="currentCarousel.image" :alt="currentCarousel.title" />
        </div>
        <div class="detail-info">
          <p><strong>ID:</strong> {{ currentCarousel.id }}</p>
          <p><strong>标题:</strong> {{ currentCarousel.title }}</p>
          <p><strong>描述:</strong> {{ currentCarousel.description }}</p>
          <p><strong>跳转链接:</strong> {{ currentCarousel.url }}</p>
          <p><strong>排序:</strong> {{ currentCarousel.sortOrder }}</p>
          <p><strong>状态:</strong> 
            <el-tag :type="currentCarousel.status === 1 ? 'success' : 'danger'">
              {{ currentCarousel.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </p>
          <p><strong>创建时间:</strong> {{ formatDate(currentCarousel.createdAt) }}</p>
          <p><strong>更新时间:</strong> {{ formatDate(currentCarousel.updatedAt) }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCarouselList, createCarousel, updateCarousel, deleteCarousel } from '@/api/carousel'

export default {
  name: 'CarouselManagement',
  data() {
    return {
      loading: false,
      carouselList: [],
      dialogVisible: false,
      viewDialogVisible: false,
      submitLoading: false,
      isEdit: false,
      currentCarousel: null,
      carouselForm: {
        title: '',
        description: '',
        image: '',
        url: '',
        sortOrder: 0,
        status: 1
      },
      formRules: {
        title: [
          { required: true, message: '请输入轮播图标题', trigger: 'blur' }
        ],
        image: [
          { required: true, message: '请输入图片URL', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑轮播图' : '新增轮播图'
    }
  },
  mounted() {
    this.fetchCarouselList()
  },
  methods: {
    // 获取轮播图列表
    async fetchCarouselList() {
      this.loading = true
      try {
        const response = await getCarouselList()
        console.log('Carousel List Response:', response)
        this.carouselList = response.data || []
      } catch (error) {
        console.error('获取轮播图列表失败:', error)
        this.$message.error('获取轮播图列表失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    // 新增轮播图
    handleAdd() {
      this.isEdit = false
      this.dialogVisible = true
    },
    // 编辑轮播图
    handleEdit(row) {
      this.isEdit = true
      this.currentCarousel = row
      this.carouselForm = {
        title: row.title,
        description: row.description,
        image: row.image,
        url: row.url,
        sortOrder: row.sortOrder,
        status: row.status
      }
      this.dialogVisible = true
    },
    // 查看轮播图详情
    handleView(row) {
      this.currentCarousel = row
      this.viewDialogVisible = true
    },
    // 删除轮播图
    handleDelete(row) {
      this.$confirm('确定要删除这个轮播图吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteCarousel(row.id)
          this.$message.success('删除成功')
          this.fetchCarouselList()
        } catch (error) {
          console.error('删除轮播图失败:', error)
          this.$message.error('删除失败，请稍后重试')
        }
      })
    },
    // 提交表单
    async handleSubmit() {
      this.$refs.carouselForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            if (this.isEdit) {
              await updateCarousel(this.currentCarousel.id, this.carouselForm)
              this.$message.success('更新成功')
            } else {
              await createCarousel(this.carouselForm)
              this.$message.success('创建成功')
            }
            this.dialogVisible = false
            this.fetchCarouselList()
          } catch (error) {
            console.error('提交失败:', error)
            this.$message.error('操作失败，请稍后重试')
          } finally {
            this.submitLoading = false
          }
        }
      })
    },
    // 重置表单
    resetForm() {
      this.carouselForm = {
        title: '',
        description: '',
        image: '',
        url: '',
        sortOrder: 0,
        status: 1
      }
      this.$refs.carouselForm && this.$refs.carouselForm.resetFields()
    },
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '-'
      const date = new Date(dateStr)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.carousel-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
      margin: 0 0 10px 0;
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #666;
    }
  }
}

.carousel-list {
  background: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .carousel-thumb {
    width: 80px;
    height: 45px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #eee;
  }
}

.carousel-detail {
  .detail-image {
    text-align: center;
    margin-bottom: 20px;

    img {
      max-width: 100%;
      max-height: 200px;
      border-radius: 8px;
      border: 1px solid #eee;
    }
  }

  .detail-info {
    p {
      margin: 10px 0;
      font-size: 14px;
      line-height: 1.6;

      strong {
        display: inline-block;
        width: 80px;
        color: #333;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .carousel-container {
    padding: 10px;

    .page-header {
      padding: 20px;

      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;

        h2 {
          font-size: 20px;
        }
      }
    }

    .carousel-list {
      padding: 15px;
      overflow-x: auto;
    }
  }
}
</style>
