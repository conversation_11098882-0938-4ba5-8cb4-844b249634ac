<template>
  <div class="home-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>首页数据展示</h2>
      <p>展示网站首页的轮播图、热门电影、最新电影和统计信息</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <i class="el-icon-loading" style="font-size: 24px;" />
      <p>正在加载首页数据...</p>
    </div>

    <!-- 首页数据展示 -->
    <div v-else-if="homeData" class="home-content">
      <!-- 统计信息卡片 -->
      <div class="stats-section">
        <h3>统计信息</h3>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon movies">
                <i class="el-icon-video-camera" />
              </div>
              <div class="stat-info">
                <h4>{{ homeData.stats.totalMovies }}</h4>
                <p>总电影数</p>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon tv">
                <i class="el-icon-monitor" />
              </div>
              <div class="stat-info">
                <h4>{{ homeData.stats.totalTVShows }}</h4>
                <p>总电视剧数</p>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon variety">
                <i class="el-icon-star-on" />
              </div>
              <div class="stat-info">
                <h4>{{ homeData.stats.totalVarietyShows }}</h4>
                <p>总综艺数</p>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon users">
                <i class="el-icon-user" />
              </div>
              <div class="stat-info">
                <h4>{{ homeData.stats.totalUsers }}</h4>
                <p>总用户数</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 轮播图展示 -->
      <div class="carousel-section">
        <h3>轮播图</h3>
        <el-carousel height="300px" indicator-position="outside">
          <el-carousel-item v-for="item in homeData.carousel" :key="item.id">
            <div class="carousel-item">
              <img :src="item.image" :alt="item.title" />
              <div class="carousel-overlay">
                <h4>{{ item.title }}</h4>
                <p>{{ item.description }}</p>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>

      <!-- 热门电影 -->
      <div class="movies-section">
        <h3>热门电影</h3>
        <el-row :gutter="20">
          <el-col v-for="movie in homeData.hotMovies" :key="movie.id" :span="6">
            <div class="movie-card">
              <div class="movie-poster">
                <img :src="movie.poster" :alt="movie.title" />
                <div class="movie-rating">
                  <i class="el-icon-star-on" />
                  {{ movie.rating }}
                </div>
              </div>
              <div class="movie-info">
                <h4>{{ movie.title }}</h4>
                <p class="movie-year">{{ movie.movieYear }}年</p>
                <p class="movie-genre">{{ parseGenre(movie.genre) }}</p>
                <div class="movie-stats">
                  <span><i class="el-icon-view" /> {{ formatNumber(movie.viewCount) }}</span>
                  <span><i class="el-icon-star-off" /> {{ formatNumber(movie.likeCount) }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 最新电影 -->
      <div class="movies-section">
        <h3>最新电影</h3>
        <el-row :gutter="20">
          <el-col v-for="movie in homeData.latestMovies" :key="movie.id" :span="6">
            <div class="movie-card">
              <div class="movie-poster">
                <img :src="movie.poster" :alt="movie.title" />
                <div class="movie-rating">
                  <i class="el-icon-star-on" />
                  {{ movie.rating }}
                </div>
              </div>
              <div class="movie-info">
                <h4>{{ movie.title }}</h4>
                <p class="movie-year">{{ movie.movieYear }}年</p>
                <p class="movie-genre">{{ parseGenre(movie.genre) }}</p>
                <div class="movie-stats">
                  <span><i class="el-icon-view" /> {{ formatNumber(movie.viewCount) }}</span>
                  <span><i class="el-icon-star-off" /> {{ formatNumber(movie.likeCount) }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-alert
        title="数据加载失败"
        type="error"
        description="无法获取首页数据，请稍后重试"
        show-icon
        :closable="false"
      />
      <el-button type="primary" style="margin-top: 20px" @click="fetchHomeData">
        重新加载
      </el-button>
    </div>
  </div>
</template>

<script>
import { getHomeData } from '@/api/home'

export default {
  name: 'Home',
  data() {
    return {
      loading: false,
      homeData: null
    }
  },
  mounted() {
    this.fetchHomeData()
  },
  methods: {
    // 获取首页数据
    async fetchHomeData() {
      this.loading = true
      try {
        const response = await getHomeData()
        console.log('Home API Response:', response)
        this.homeData = response.data
      } catch (error) {
        console.error('获取首页数据失败:', error)
        this.$message.error('获取首页数据失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    // 解析电影类型
    parseGenre(genreStr) {
      try {
        const genres = JSON.parse(genreStr)
        return Array.isArray(genres) ? genres.join(', ') : genreStr
      } catch {
        return genreStr
      }
    },
    // 格式化数字显示
    formatNumber(num) {
      if (num >= 100000000) {
        return (num / 100000000).toFixed(1) + '亿'
      } else if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toString()
    }
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 8px;
  margin-bottom: 20px;

  h2 {
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
  }

  p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;

  p {
    margin-top: 20px;
    color: #666;
    font-size: 16px;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px;
}

.home-content {
  .stats-section,
  .carousel-section,
  .movies-section {
    background: white;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 20px 0;
      font-size: 20px;
      font-weight: 600;
      color: #333;
      border-bottom: 2px solid #409EFF;
      padding-bottom: 10px;
    }
  }

  .stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 20px;
        color: white;
      }

      &.movies {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.tv {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.variety {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.users {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }

    .stat-info {
      h4 {
        margin: 0 0 5px 0;
        font-size: 24px;
        font-weight: 700;
        color: #333;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: #666;
      }
    }
  }

  .carousel-item {
    position: relative;
    height: 300px;
    overflow: hidden;
    border-radius: 8px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .carousel-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
      color: white;
      padding: 30px 20px 20px;

      h4 {
        margin: 0 0 10px 0;
        font-size: 20px;
        font-weight: 600;
      }

      p {
        margin: 0;
        font-size: 14px;
        opacity: 0.9;
      }
    }
  }

  .movie-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    margin-bottom: 20px;

    &:hover {
      transform: translateY(-2px);
    }

    .movie-poster {
      position: relative;
      height: 200px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .movie-rating {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.7);
        color: #FFD700;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 600;

        i {
          margin-right: 3px;
        }
      }
    }

    .movie-info {
      padding: 15px;

      h4 {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .movie-year {
        margin: 0 0 5px 0;
        font-size: 12px;
        color: #999;
      }

      .movie-genre {
        margin: 0 0 10px 0;
        font-size: 12px;
        color: #666;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .movie-stats {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #999;

        span {
          i {
            margin-right: 3px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .home-container {
    .stats-section,
    .movies-section {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .home-container {
    padding: 10px;

    .page-header {
      padding: 20px;

      h2 {
        font-size: 24px;
      }
    }

    .home-content {
      .stat-card {
        padding: 15px;

        .stat-icon {
          width: 40px;
          height: 40px;
          margin-right: 10px;

          i {
            font-size: 16px;
          }
        }

        .stat-info {
          h4 {
            font-size: 20px;
          }
        }
      }

      .movie-card {
        .movie-poster {
          height: 150px;
        }

        .movie-info {
          padding: 10px;
        }
      }
    }
  }
}
</style>
