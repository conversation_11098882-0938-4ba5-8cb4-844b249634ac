const Mock = require('mockjs')

// Mock数据
const homeData = {
  carousel: [
    {
      id: 1,
      title: '阿凡达：水之道',
      description: '詹姆斯·卡梅隆执导的科幻史诗巨作',
      image: 'https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2876408703.jpg',
      url: '/movie/1',
      sortOrder: 1,
      status: 1,
      createdAt: '2025-06-16T10:00:00',
      updatedAt: '2025-06-16T10:00:00'
    },
    {
      id: 2,
      title: '流浪地球2',
      description: '中国科幻电影的新里程碑',
      image: 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2885955371.jpg',
      url: '/movie/2',
      sortOrder: 2,
      status: 1,
      createdAt: '2025-06-16T10:00:00',
      updatedAt: '2025-06-16T10:00:00'
    },
    {
      id: 3,
      title: '满江红',
      description: '张艺谋执导的古装悬疑片',
      image: 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2886345662.jpg',
      url: '/movie/3',
      sortOrder: 3,
      status: 1,
      createdAt: '2025-06-16T10:00:00',
      updatedAt: '2025-06-16T10:00:00'
    }
  ],
  hotMovies: [
    {
      id: 1,
      title: '阿凡达：水之道',
      originalTitle: 'Avatar: The Way of Water',
      poster: 'https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2876408703.jpg',
      rating: 8.5,
      movieYear: 2022,
      duration: 192,
      genre: '["科幻", "动作", "冒险"]',
      region: '美国',
      director: '詹姆斯·卡梅隆',
      viewCount: 1500000,
      likeCount: 85000
    },
    {
      id: 2,
      title: '流浪地球2',
      originalTitle: 'The Wandering Earth II',
      poster: 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2885955371.jpg',
      rating: 8.3,
      movieYear: 2023,
      duration: 173,
      genre: '["科幻", "灾难", "动作"]',
      region: '中国',
      director: '郭帆',
      viewCount: 2800000,
      likeCount: 120000
    },
    {
      id: 3,
      title: '满江红',
      originalTitle: 'Full River Red',
      poster: 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2886345662.jpg',
      rating: 7.9,
      movieYear: 2023,
      duration: 159,
      genre: '["剧情", "悬疑", "古装"]',
      region: '中国',
      director: '张艺谋',
      viewCount: 2200000,
      likeCount: 95000
    },
    {
      id: 4,
      title: '黑豹2：瓦坎达万岁',
      originalTitle: 'Black Panther: Wakanda Forever',
      poster: 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2875854806.jpg',
      rating: 7.2,
      movieYear: 2022,
      duration: 161,
      genre: '["动作", "科幻", "冒险"]',
      region: '美国',
      director: '瑞恩·库格勒',
      viewCount: 980000,
      likeCount: 45000
    }
  ],
  latestMovies: [
    {
      id: 5,
      title: '深海',
      originalTitle: 'Deep Sea',
      poster: 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2886345662.jpg',
      rating: 7.8,
      movieYear: 2023,
      duration: 112,
      genre: '["动画", "奇幻", "冒险"]',
      region: '中国',
      director: '田晓鹏',
      viewCount: 650000,
      likeCount: 38000
    },
    {
      id: 6,
      title: '中国乒乓之绝地反击',
      originalTitle: 'Ping Pong: The Triumph',
      poster: 'https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2885955371.jpg',
      rating: 7.5,
      movieYear: 2023,
      duration: 140,
      genre: '["剧情", "运动", "传记"]',
      region: '中国',
      director: '邓超',
      viewCount: 420000,
      likeCount: 25000
    },
    {
      id: 7,
      title: '无名',
      originalTitle: 'Hidden Blade',
      poster: 'https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2876408703.jpg',
      rating: 7.1,
      movieYear: 2023,
      duration: 125,
      genre: '["剧情", "动作", "悬疑"]',
      region: '中国',
      director: '程耳',
      viewCount: 380000,
      likeCount: 22000
    },
    {
      id: 8,
      title: '交换人生',
      originalTitle: 'Life Exchange',
      poster: 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2875854806.jpg',
      rating: 6.8,
      movieYear: 2023,
      duration: 108,
      genre: '["喜剧", "奇幻", "家庭"]',
      region: '中国',
      director: '苏伦',
      viewCount: 290000,
      likeCount: 18000
    }
  ],
  stats: {
    totalMovies: 11,
    totalTVShows: 5,
    totalVarietyShows: 5,
    totalUsers: 5
  }
}

const carouselList = [
  {
    id: 1,
    title: '阿凡达：水之道',
    description: '詹姆斯·卡梅隆执导的科幻史诗巨作',
    image: 'https://img2.doubanio.com/view/photo/s_ratio_poster/public/p2876408703.jpg',
    url: '/movie/1',
    sortOrder: 1,
    status: 1,
    createdAt: '2025-06-16T10:00:00',
    updatedAt: '2025-06-16T10:00:00'
  },
  {
    id: 2,
    title: '流浪地球2',
    description: '中国科幻电影的新里程碑',
    image: 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2885955371.jpg',
    url: '/movie/2',
    sortOrder: 2,
    status: 1,
    createdAt: '2025-06-16T10:00:00',
    updatedAt: '2025-06-16T10:00:00'
  },
  {
    id: 3,
    title: '满江红',
    description: '张艺谋执导的古装悬疑片',
    image: 'https://img1.doubanio.com/view/photo/s_ratio_poster/public/p2886345662.jpg',
    url: '/movie/3',
    sortOrder: 3,
    status: 0,
    createdAt: '2025-06-16T10:00:00',
    updatedAt: '2025-06-16T10:00:00'
  }
]

module.exports = [
  // 获取首页数据
  {
    url: '/vue-admin-template/home',
    type: 'get',
    response: config => {
      return {
        code: 200,
        message: 'success',
        data: homeData
      }
    }
  },

  // 获取轮播图列表
  {
    url: '/vue-admin-template/carousel',
    type: 'get',
    response: config => {
      return {
        code: 200,
        message: 'success',
        data: carouselList
      }
    }
  },

  // 获取轮播图详情
  {
    url: '/vue-admin-template/carousel/\\d+',
    type: 'get',
    response: config => {
      const id = parseInt(config.url.split('/').pop())
      const carousel = carouselList.find(item => item.id === id)
      return {
        code: 200,
        message: 'success',
        data: carousel || null
      }
    }
  },

  // 创建轮播图
  {
    url: '/vue-admin-template/carousel',
    type: 'post',
    response: config => {
      const newCarousel = {
        id: carouselList.length + 1,
        ...config.body,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      carouselList.push(newCarousel)
      return {
        code: 200,
        message: 'success',
        data: newCarousel
      }
    }
  },

  // 更新轮播图
  {
    url: '/vue-admin-template/carousel/\\d+',
    type: 'put',
    response: config => {
      const id = parseInt(config.url.split('/').pop())
      const index = carouselList.findIndex(item => item.id === id)
      if (index !== -1) {
        carouselList[index] = {
          ...carouselList[index],
          ...config.body,
          updatedAt: new Date().toISOString()
        }
        return {
          code: 200,
          message: 'success',
          data: carouselList[index]
        }
      }
      return {
        code: 404,
        message: '轮播图不存在'
      }
    }
  },

  // 删除轮播图
  {
    url: '/vue-admin-template/carousel/\\d+',
    type: 'delete',
    response: config => {
      const id = parseInt(config.url.split('/').pop())
      const index = carouselList.findIndex(item => item.id === id)
      if (index !== -1) {
        carouselList.splice(index, 1)
        return {
          code: 200,
          message: 'success',
          data: '轮播图删除成功'
        }
      }
      return {
        code: 404,
        message: '轮播图不存在'
      }
    }
  }
]
